<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - CouponHub</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <span title="Ticket">🎟️</span>
                    <span>CouponHub</span>
                </div>
                <div>
                    <a href="cart-actions.html" class="btn btn--outline">Back to Cart</a>
                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="payment-section">
            <div class="container">
                <h2>Payment</h2>
                <div id="orderSummary">
                    <!-- Order summary will be rendered here -->
                </div>
                <form id="paymentForm" style="margin-top:2rem;">
                    <div class="form-group">
                        <label class="form-label">Payment Method</label>
                        <select class="form-control" id="paymentMethod" required>
                            <option value="razorpay">Razorpay (UPI/Cards/Net Banking)</option>
                            <option value="stripe">Stripe (International Cards)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn--primary btn--full-width" id="payNowBtn">
                            Pay Now
                        </button>
                    </div>
                </form>
            </div>
        </section>
    </main>
    <div class="toast-container" id="toastContainer"></div>
    <script>
        // Utility: show toast
        function showToast(message, type = 'info') {
            const container = document.getElementById('toastContainer');
            if (!container) return;
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `<span>${message}</span>`;
            container.appendChild(toast);
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => { if (container.contains(toast)) container.removeChild(toast); }, 300);
            }, 3000);
        }

        // Render order summary
        function renderOrderSummary() {
            const orderSummary = document.getElementById('orderSummary');
            let cart = [];
            try {
                cart = JSON.parse(localStorage.getItem('couponhub_cart')) || [];
            } catch { cart = []; }
            if (cart.length === 0) {
                orderSummary.innerHTML = '<p class="empty-cart">Your cart is empty. <a href="index.html">Go to Shop</a></p>';
                document.getElementById('payNowBtn').disabled = true;
                return;
            }
            let total = cart.reduce((sum, item) => sum + item.price, 0);
            let platformFee = Math.round(total * 0.05);
            let finalTotal = total + platformFee;
            orderSummary.innerHTML = `
                <ul class="cart-list">
                    ${cart.map(item => `
                        <li class="cart-list-item">
                            <img src="${item.image}" alt="${item.title}" style="width:40px;height:40px;object-fit:cover;margin-right:1rem;">
                            <span>${item.title}</span>
                            <span style="margin-left:auto;">₹${item.price.toLocaleString()}</span>
                        </li>
                    `).join('')}
                </ul>
                <div class="cart-total" style="margin-top:1rem;">
                    <div>Subtotal: ₹${total.toLocaleString()}</div>
                    <div>Platform Fee (5%): ₹${platformFee.toLocaleString()}</div>
                    <div style="font-weight:bold;">Total: ₹${finalTotal.toLocaleString()}</div>
                </div>
                <div class="escrow-notice" style="margin-top:1rem; padding:1rem; background:var(--color-bg-3); border-radius:var(--radius-base); font-size:var(--font-size-sm);">
                    <span>🔒</span> Your payment will be held in secure escrow until you confirm receipt of the items.
                </div>
            `;
            document.getElementById('payNowBtn').disabled = false;
        }

        // Handle payment
        document.getElementById('paymentForm').onsubmit = function(e) {
            e.preventDefault();
            let cart = [];
            try {
                cart = JSON.parse(localStorage.getItem('couponhub_cart')) || [];
            } catch { cart = []; }
            if (cart.length === 0) {
                showToast('Cart is empty', 'error');
                return;
            }
            showToast('Processing payment...', 'info');
            document.getElementById('payNowBtn').disabled = true;
            setTimeout(() => {
                localStorage.setItem('couponhub_cart', '[]');
                showToast('Payment successful! Items will be delivered shortly.', 'success');
                setTimeout(() => { window.location.href = 'index.html'; }, 1500);
            }, 1800);
        };

        // Initial render
        renderOrderSummary();
    </script>
</body>
</html> 
