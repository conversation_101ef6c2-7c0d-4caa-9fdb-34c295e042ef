<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Actions - CouponHub</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <span title="Ticket">🎟️</span>
                    <span>CouponHub</span>
                </div>
                <div>
                    <a href="index.html" class="btn btn--outline">Back to Shop</a>
                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="cart-actions-section">
            <div class="container">
                <h2>Your Cart</h2>
                <div id="cartSummary">
                    <!-- Cart items will be rendered here -->
                </div>
                <div class="cart-actions-buttons" style="margin-top:2rem;">
                    <button class="btn btn--outline btn--full-width" id="cancelCartBtn">Cancel Cart</button>
                    <button class="btn btn--primary btn--full-width" id="proceedPaymentBtn" style="margin-top:1rem;">Proceed for Payment</button>
                </div>
            </div>
        </section>
    </main>
    <div class="toast-container" id="toastContainer"></div>
    <script>
        // Utility: show toast
        function showToast(message, type = 'info') {
            const container = document.getElementById('toastContainer');
            if (!container) return;
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `<span>${message}</span>`;
            container.appendChild(toast);
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => { if (container.contains(toast)) container.removeChild(toast); }, 300);
            }, 3000);
        }

        // Render cart summary
        function renderCart() {
            const cartSummary = document.getElementById('cartSummary');
            let cart = [];
            try {
                cart = JSON.parse(localStorage.getItem('couponhub_cart')) || [];
            } catch { cart = []; }
            if (cart.length === 0) {
                cartSummary.innerHTML = '<p class="empty-cart">Your cart is empty.</p>';
                document.getElementById('proceedPaymentBtn').disabled = true;
                document.getElementById('cancelCartBtn').disabled = true;
                return;
            }
            let total = cart.reduce((sum, item) => sum + item.price, 0);
            cartSummary.innerHTML = `
                <ul class="cart-list">
                    ${cart.map(item => `
                        <li class="cart-list-item">
                            <img src="${item.image}" alt="${item.title}" style="width:40px;height:40px;object-fit:cover;margin-right:1rem;">
                            <span>${item.title}</span>
                            <span style="margin-left:auto;">₹${item.price.toLocaleString()}</span>
                        </li>
                    `).join('')}
                </ul>
                <div class="cart-total" style="margin-top:1rem;font-weight:bold;">Total: ₹${total.toLocaleString()}</div>
            `;
            document.getElementById('proceedPaymentBtn').disabled = false;
            document.getElementById('cancelCartBtn').disabled = false;
        }

        // Cancel cart
        document.getElementById('cancelCartBtn').onclick = function() {
            localStorage.setItem('couponhub_cart', '[]');
            renderCart();
            showToast('Cart cancelled', 'success');
            setTimeout(() => { window.location.href = 'index.html'; }, 1200);
        };

        // Proceed for payment
        document.getElementById('proceedPaymentBtn').onclick = function() {
            window.location.href = 'payment.html';
        };

        // Initial render
        renderCart();
    </script>
</body>
</html>
